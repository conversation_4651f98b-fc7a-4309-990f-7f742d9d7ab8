# 充值订单相关接口文档

## 域名

| 环境  | 域名          |
|-----|-------------|
| qa  | http://xxxx |
| stg | http://xxxx |
| prd | http://xxxx |

## 共5个接口
- 卡校验接口
- 充值订单推送接口
- 充值订单变更接口
- 充值订单充值接口
- 充值完成回调接口（由财管提供）

## 1. 卡校验接口

。。。。。。。。。。。。。。。。。。。。。。。。。




## 2. 充值订单推送接口

### 接口说明

财管合同审批通过后推送充值订单到万达云，推送信息包含充值订单号、卡号、充值金额、赠送金额等字段

### 请求地址

POST /card-kam/ysht/recharge-order/push

### 请求参数

| 参数名                     | 类型     | 必填 | 说明                              |
|-------------------------|--------|----|---------------------------------|
| rechargeOrderNo         | String | 是  | 充值订单号                           |
| contractNo              | String | 是  | 合同编码                            |
| customerCode            | String | 是  | 客户编码                            |
| customerName            | String | 是  | 客户名称                            |
| sellerId                | String | 是  | 销售员万信号                          |
| seller                  | String | 是  | 销售员姓名                           |
| areaCode                | String | 是  | 区域编码                            |
| areaName                | String | 是  | 区域名称                            |
| cinemaInnerCode         | String | 是  | 影城内码                            |
| cinemaName              | String | 是  | 影城名称                            |
| everyCardRechargeAmount | Number | 是  | 每张卡充值金额(分)                      |
| everyCardPresentAmount  | Number | 是  | 每张卡赠送金额(分)                      |
| count                   | Number | 是  | 卡总数                             |
| amount                  | Number | 是  | 总充值金额（含赠送金额）分,卡数量*单卡(充值金额+赠送金额) |
| cardInfos               | Array  | 是  | 卡信息列表                           |

#### cardInfos参数说明

| 参数名          | 类型     | 必填 | 说明    |
|--------------|--------|----|-------|
| cardNo       | String | 是  | 卡号    |
| cardTypeCode | String | 是  | 卡类型编码 |
| cardTypeName | String | 是  | 卡类型名称 |

### 请求示例

```json
{
  "seller": "张三",
  "sellerId": "S001",
  "amount": 50000,
  "count": 2,
  "everyCardRechargeAmount": 24000,
  "everyCardPresentAmount": 1000,
  "rechargeOrderNo": "RO20230801001",
  "contractNo": "CT20230801001",
  "customerCode": "CUST001",
  "customerName": "北京万达影城",
  "areaCode": "AC001",
  "areaName": "华北区",
  "cinemaInnerCode": "CI001",
  "cinemaName": "北京万达影城CBD店",
  "cardInfos": [
    {
      "cardTypeName": "储值卡",
      "cardTypeCode": "001",
      "cardNo": "1000000001"
    },
    {
      "cardTypeName": "储值卡",
      "cardTypeCode": "002",
      "cardNo": "1000000002"
    }
  ]
}
```

### 响应参数

| 参数名  | 类型     | 说明         |
|------|--------|------------|
| code | Number | 0:成功 非0:失败 |
| msg  | String | 失败提示信息     |

### 响应示例

```json
{
  "msg": "成功",
  "code": 0
}
```

## 3. 充值订单变更接口
。。。。。。。。。。。。。。。。。。。



## 4. 充值订单充值接口（充值操作异步执行）

### 接口说明

对已推送的充值订单执行充值操作（卡充值操作异步执行）

### 请求地址

POST /card-kam/ysht/recharge-order/card-recharge

### 请求参数

| 参数名                     | 类型     | 必填 | 说明           |
|-------------------------|--------|----|--------------|
| batchNo                 | String | 是  | 充值请求流水号(批次号) |
| contractNo              | String | 是  | 合同号          |
| rechargeOrderNo         | String | 是  | 充值订单号        |
| everyCardRechargeAmount | Number | 是  | 每张卡充值金额（分）   |
| everyCardPresentAmount  | Number | 是  | 每张卡赠送金额（分）   |
| cardNos                 | Array  | 是  | 卡号列表         |

### 请求示例

```json
{
  "batchNo": "B20230801001",
  "contractNo": "CT20230801001",
  "rechargeOrderNo": "RO20230801001",
  "everyCardPresentAmount": 1000,
  "everyCardRechargeAmount": 24000,
  "cardNos": [
    "1000000001",
    "1000000002"
  ]
}
```

### 响应参数

| 参数名  | 类型     | 说明         |
|------|--------|------------|
| code | Number | 0:成功 非0:失败 |
| msg  | String | 失败提示信息     |

### 响应示例

```json
{
  "msg": "成功",
  "code": 0
}
```
## 5. 充值完成回调接口（由财管提供）

### 接口说明

万达云卡充值完成回调通知财管接口

### 请求地址

POST 

### 请求参数

| 参数名                     | 类型     | 必填 | 说明           |
|-------------------------|--------|----|--------------|
| batchNo                 | String | 是  | 充值请求流水号(批次号) |
| contractNo              | String | 是  | 合同号          |
| rechargeOrderNo         | String | 是  | 充值订单号        |
| everyCardRechargeAmount | Number | 是  | 每张卡充值金额（分）   |
| everyCardPresentAmount  | Number | 是  | 每张卡赠送金额（分）   |
| cardInfos               | Array  | 是  | 卡号列表         |

#### cardInfos参数说明

| 参数名                | 类型     | 必填 | 说明                   |
|--------------------|--------|----|----------------------|
| cardNo             | String | 是  | 卡号                   |
| rechargeResult     | bool   | 是  | true为充值成功，false为充值失败 |
| rechargeFailResult | String | 否  | 失败信息                 |

### 请求示例

```json
{
  "batchNo": "B20230801001",
  "rechargeOrderNo": "RO20230801001",
  "contractNo": "CT20230801001",
  "everyCardPresentAmount": 1000,
  "everyCardRechargeAmount": 24000,
  "cardInfos": [
    {
      "cardNo": "1000000001",
      "rechargeResult": true,
      "rechargeFailResult": ""
    },
    {
      "cardNo": "1000000002",
      "rechargeResult": false,
      "rechargeFailResult": "卡状态冻结"
    }
  ]
}
```

### 响应参数

| 参数名  | 类型     | 说明         |
|------|--------|------------|
| code | Number | 0:成功 非0:失败 |
| msg  | String | 失败提示信息     |

### 响应示例

```json
{
  "msg": "成功",
  "code": 0
}
```