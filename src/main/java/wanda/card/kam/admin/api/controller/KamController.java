package wanda.card.kam.admin.api.controller;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.api.model.PushMapper;
import wanda.card.kam.admin.api.model.RechargeCardModel;
import wanda.card.kam.admin.api.model.RechargeOrderPushModel.RechargeOrderPushRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.OrderUpdateRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyRequest;
import wanda.card.kam.admin.api.model.RechargeVerifyModel.VerifyResponse;
import wanda.card.kam.admin.api.model.Result;
import wanda.card.kam.admin.contract.RechargeOrderPushService;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto;
import wanda.stark.core.data.R;

import javax.validation.Valid;

/**
 * 给财管使用
 */
@Slf4j
@RestController
@RequestMapping("/card-kam/ysht")
@AllArgsConstructor
public class KamController {
    private final PushMapper pushMapper;
    private final RechargeOrderPushService rechargeOrderPushService;

    /**
     * 充值验证
     */
    @PostMapping(value = "recharge/verify")
    public Result<VerifyResponse> rechargeVerify(@RequestBody VerifyRequest request) {
        //批量查询卡信息,校验卡状态
        //校验，客户信息
        //校验卡类型是否可充值，最低充值金额限制
        return Result.success();
    }

    /**
     * 充值订单变更接口
     */
    @PostMapping(value = "/recharge-order/update")
    public Result<VerifyResponse> rechargeOrderUpdate(@RequestBody OrderUpdateRequest request) {
        //批量查询卡信息,校验卡状态
        //校验，客户信息
        //校验卡类型是否可充值，最低充值金额限制
        return Result.success();
    }

    @PostMapping(value = "recharge-order/push")
    public Result<Void> rechargeOrderPush(@Valid @RequestBody RechargeOrderPushRequest request) {
        log.info("充值订单推送入参:{}", request);
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = pushMapper.convertToRpcRequest(request);
        try {
            R rpcResponse = rechargeOrderPushService.pushRechargeOrder(rpcRequest);
            log.info("充值订单推送返回:{}", JSON.toJSONString(rpcResponse));
            if (rpcResponse.isSuccess())
                return Result.success();
            return Result.simple(-1, rpcResponse.getMsg());
        } catch (Exception e) {
            log.error(">>>充值订单推送异常", e);
            return Result.simple(-1, e.getMessage());
        }
    }

    @PostMapping(value = "recharge-order/recharge")
    public Result<Void> rechargeCard(@Valid @RequestBody RechargeCardModel.RechargeCardRequest request) {
        log.info("充值订单充值入参:{}", request);
//        log.info("充值订单充值返回:{}", response);
        return Result.success();
    }


}