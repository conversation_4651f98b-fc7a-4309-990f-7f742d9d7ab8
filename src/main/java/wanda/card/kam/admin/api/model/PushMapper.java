package wanda.card.kam.admin.api.model;

import org.springframework.stereotype.Component;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class PushMapper {
    /**
     * 转换API请求模型为RPC DTO
     */
    public RechargeOrderPushDto.RechargeOrderPushRequest convertToRpcRequest(RechargeOrderPushModel.RechargeOrderPushRequest apiRequest) {
        RechargeOrderPushDto.RechargeOrderPushRequest rpcRequest = new RechargeOrderPushDto.RechargeOrderPushRequest();
        rpcRequest.setRechargeOrderNo(apiRequest.getRechargeOrderNo());
        rpcRequest.setContractNo(apiRequest.getContractNo());
        rpcRequest.setCustomerCode(apiRequest.getCustomerCode());
        rpcRequest.setCustomerName(apiRequest.getCustomerName());
        rpcRequest.setSellerId(apiRequest.getSellerId());
        rpcRequest.setSeller(apiRequest.getSeller());
        rpcRequest.setAreaCode(apiRequest.getAreaCode());
        rpcRequest.setAreaName(apiRequest.getAreaName());
        rpcRequest.setCinemaInnerCode(apiRequest.getCinemaInnerCode());
        rpcRequest.setCinemaName(apiRequest.getCinemaName());

        // 转换卡信息列表
        List<RechargeOrderPushDto.RechargeOrderCardPushInfo> rpcCardInfos = apiRequest.getCardInfos().stream()
                .map(this::convertToRpcCardInfo)
                .collect(Collectors.toList());
        rpcRequest.setCardInfos(rpcCardInfos);

        return rpcRequest;
    }

    /**
     * 转换API卡信息为RPC卡信息
     */
    public RechargeOrderPushDto.RechargeOrderCardPushInfo convertToRpcCardInfo(
            wanda.card.kam.admin.api.model.RechargeOrderPushModel.RechargeOrderCardPushInfo apiCardInfo) {
        RechargeOrderPushDto.RechargeOrderCardPushInfo rpcCardInfo = new RechargeOrderPushDto.RechargeOrderCardPushInfo();
        rpcCardInfo.setCardNo(apiCardInfo.getCardNo());
        rpcCardInfo.setCardTypeCode(apiCardInfo.getCardTypeCode());
        rpcCardInfo.setCardTypeName(apiCardInfo.getCardTypeName());
        return rpcCardInfo;
    }

}
