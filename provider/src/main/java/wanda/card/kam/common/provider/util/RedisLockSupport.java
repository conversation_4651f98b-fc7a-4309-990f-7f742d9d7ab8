package wanda.card.kam.common.provider.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import wanda.stark.core.lang.FaultException;

import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
@Component
public class RedisLockSupport {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    private static final Long EXPIRE_TIME = 20L;
    private static final String LOCK_PREFIX = "CARD_KAM_RECHARGE_ORDER_";

    private boolean canLock(String key) {
        // "SET" "${key}" "\"${value}\"" "EX" "${lockTimeOut}" "NX"
        // setIfAbsent 对应redis命令如上，通过set命令的参数NX，完成排斥锁和原子性
        return redisTemplate.opsForValue().setIfAbsent(LOCK_PREFIX + key, "true", EXPIRE_TIME, TimeUnit.SECONDS);
    }

    public Object optimisticLockTemplate(String key, Supplier<Object> supplier) {
        tryOptimisticLock(key);
        try {
            return supplier.get();
        } finally {
            unLock(key);
        }
    }

    public void optimisticLockTemplate(String key, Runnable chunk) {
        tryOptimisticLock(key);
        try {
            chunk.run();
        } finally {
            unLock(key);
        }
    }

    public Object lockTemplate(String key, Supplier<Object> supplier) {
        tryLock(key);
        try {
            return supplier.get();
        } finally {
            unLock(key);
        }
    }

    public void lockTemplate(String key, Runnable chunk) {
        tryLock(key);
        try {
            chunk.run();
        } finally {
            unLock(key);
        }
    }

    public void tryOptimisticLock(String key) {
        log.info(">>>开始获得乐观锁,key={}", key);
        if (!canLock(key)) {
            throw new FaultException("key=" + key + "已被锁,不能执行");
        }
    }

    public void tryLock(String key) {
        log.info(">>>开始获得锁,key={}", key);
        long expire = System.currentTimeMillis() + (EXPIRE_TIME * 1000L);
        while (true) {
            if (isTimeout(expire))
                throw new FaultException("LOCK_TIMEOUT");
            if (canLock(key)) {
                break;
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("lock key {} error", key, e);
                Thread.currentThread().interrupt();
            }
        }
    }

    private boolean isTimeout(long expire) {
        return System.currentTimeMillis() - expire >= 0;
    }

    /**
     * 释放锁
     *
     * @param key 锁标识
     * @return
     */
    public void unLock(String key) {
        log.info(">>>开始释放锁,key={}", key);
        try {
            redisTemplate.delete(LOCK_PREFIX + key);
        } catch (Exception e) {
            throw new FaultException("释放锁失败,key=" + key);
        }
    }
}


