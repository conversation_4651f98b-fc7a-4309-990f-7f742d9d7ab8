package wanda.card.kam.common.provider.entity;

import lombok.Getter;
import lombok.Setter;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.stark.core.db.DbEntity;

import javax.persistence.Id;
import javax.persistence.Table;
import java.time.LocalDateTime;

@Getter
@Setter
@Table
public class RechargeOrderLog implements DbEntity<Long> {
    /**
     * id
     */
    @Id
    private Long id;

    /**
     * 充值流水号（批次号）
     */
    private String batchNo;

    /**
     * 充值订单号
     */
    private String rechargeOrderNo;

    /**
     * 时间
     */
    private LocalDateTime createTime;

    /**
     * 操作类型
     */
    private OperationLogType operationType;

    /**
     * 操作日志
     */
    private String operationLog;

    /**
     * 操作人
     */
    private String operator;
}