package wanda.card.kam.common.provider.util;

import java.math.BigDecimal;

/**
 * Created by fuyuanpu on 2022/12/7.
 */
public class MoneyUtil {
    public static double fenToYuan(int fen) {
        return new BigDecimal(fen).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    public static String fenToYuanStr(int fen) {
        return String.valueOf(fenToYuan(fen));
    }

    public static String fenToYuanStrExcludeDecimalPoint(int fen) {
        String yuan = fenToYuanStr(fen);
        if (yuan.endsWith(".0"))
            return yuan.replaceAll("\\.0", "");
        if (yuan.endsWith(".00"))
            return yuan.replaceAll("\\.00", "");
        return yuan;
    }


    public static int yuanToFen(String yuan) {
        return yuanToFen(yuan(yuan));
    }

    public static int yuanToFen(double yuan) {
        return BigDecimal.valueOf(yuan).multiply(BigDecimal.valueOf(100)).intValue();
    }

    private static double yuan(String yuan) {
        return new BigDecimal(yuan).doubleValue();
    }

    public static String yuanToFenStr(double yuan) {
        return String.valueOf(yuanToFen(yuan));
    }

    public static boolean isMoneyFormat(String num) {
        return num.matches("(^[1-9]([0-9]+)?(\\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\\.[0-9]([0-9])?$)");
    }

    public static void main(String[] args) {
        System.out.println(fenToYuanStrExcludeDecimalPoint(5000));
//        System.out.println(fenToYuanStrExcludeDecimalPoint(5001));
        System.out.println("123.01".endsWith(".00") + "," + "123.00".replaceAll("\\.00", ""));
        System.out.println("0-" + isMoneyFormat("0"));
        System.out.println("0.9-" + isMoneyFormat("0.90"));
        System.out.println("0.99-" + isMoneyFormat("0.99"));
        System.out.println("0.999-" + isMoneyFormat("0.999"));
        System.out.println("1.999-" + isMoneyFormat("1.999"));
        System.out.println("1.99-" + isMoneyFormat("1.99"));
        System.out.println("1.9-" + isMoneyFormat("1.90"));
        System.out.println("100-" + isMoneyFormat("100"));
        System.out.println("100.9-" + isMoneyFormat("100.9"));
        System.out.println("100.99-" + isMoneyFormat("100.99"));
        System.out.println("100.999-" + isMoneyFormat("100.999"));
//        System.out.println(fenToYuan(10000));
//        System.out.println(fenToYuanStr(122));
//        System.out.println(fenToYuan(1227));
//        System.out.println(fenToYuan(1227));
//        System.out.println(fenToYuan(1227));
//        System.out.println(fenToYuan(1299));
        System.out.println(">>>>>>>>>>>>>>>>");
//        System.out.println(yuanToFen(1));
//        System.out.println(yuanToFen(100));
//        System.out.println(yuanToFen(100.12));
//        System.out.println(yuanToFen("100.12"));
//        System.out.println(yuanToFen("100.99"));
//        System.out.println(yuanToFen("100.999"));
    }
}
