package wanda.card.kam.common.provider.entity;

import lombok.Getter;
import lombok.Setter;
import wanda.stark.core.db.DbEntity;

import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 每批次充值卡 卡号明细
 */
@Getter
@Setter
@Table
public class BatchRechargeCardDetailLog implements DbEntity<Long> {
    /**
     * id
     */
    @Id
    private Long id;
    /**
     * 日志id
     */
    private Long logId;
    /**
     * 充值流水号（批次号）
     */
    private String batchNo;
    /**
     * 卡号
     */
    private String cardNo;
}