package wanda.card.kam.common.provider.dao;

import org.springframework.stereotype.Repository;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;
import wanda.stark.db.jsd.JsdReadWriteDao;
import wanda.stark.db.jsd.lang.Sorters;

import java.util.List;

import static wanda.card.kam.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.Shortcut.f;
import static wanda.stark.db.jsd.lang.SortType.DESC;

/**
 * 充值订单日志DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderLogDao extends JsdReadWriteDao<RechargeOrderLog, Long> {

    public RechargeOrderLogDao() {
        super(DB_NAME);
    }

    /**
     * 根据充值订单号查询日志列表
     *
     * @param rechargeOrderNo 充值订单号
     * @return 日志列表
     */
    public List<RechargeOrderLog> findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrderLog.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .orderBy(Sorters.create().add(DESC, "create_time"))
                .result()
                .all(RechargeOrderLog.class);
    }
}
