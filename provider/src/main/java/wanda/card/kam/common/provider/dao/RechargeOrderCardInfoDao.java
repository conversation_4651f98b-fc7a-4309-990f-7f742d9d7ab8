package wanda.card.kam.common.provider.dao;

import org.springframework.stereotype.Repository;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.stark.db.jsd.JsdReadWriteDao;

import java.util.List;

import static wanda.card.kam.common.provider.dao.BaseDao.DB_NAME;
import static wanda.stark.db.jsd.lang.Shortcut.f;

/**
 * 充值订单卡信息DAO
 *
 * <AUTHOR>
 */
@Repository
public class RechargeOrderCardInfoDao extends JsdReadWriteDao<RechargeOrderCardInfo, Long> {

    public RechargeOrderCardInfoDao() {
        super(DB_NAME);
    }

    /**
     * 根据充值订单号查询卡信息列表
     *
     * @param rechargeOrderNo 充值订单号
     * @return 卡信息列表
     */
    public List<RechargeOrderCardInfo> findByRechargeOrderNo(String rechargeOrderNo) {
        return openWrite().select(RechargeOrderCardInfo.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result()
                .all(RechargeOrderCardInfo.class);
    }

    /**
     * 根据充值订单号删除卡信息
     *
     * @param rechargeOrderNo 充值订单号
     * @return 删除的记录数
     */
    public void deleteByRechargeOrderNo(String rechargeOrderNo) {
        openWrite().delete(RechargeOrderCardInfo.class)
                .where(f("recharge_order_no", rechargeOrderNo))
                .result();
    }
}
