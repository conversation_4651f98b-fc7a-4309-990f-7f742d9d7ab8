package wanda.card.kam.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderCardPushInfo;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushResponse;
import wanda.card.kam.common.contract.constant.BusinessException;
import wanda.card.kam.common.contract.constant.CardRechargeStatus;
import wanda.card.kam.common.contract.constant.OperationLogType;
import wanda.card.kam.common.contract.constant.RechargeOrderStatus;
import wanda.card.kam.common.provider.dao.RechargeOrderDao;
import wanda.card.kam.common.provider.entity.RechargeOrder;
import wanda.card.kam.common.provider.entity.RechargeOrderCardInfo;
import wanda.card.kam.common.provider.entity.RechargeOrderLog;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 充值订单推送服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class RechargeOrderPushServiceBiz {
    private final RechargeOrderDao rechargeOrderDao;

    public void pushRechargeOrder(RechargeOrderPushRequest request) {
        log.info("充值订单推送服务开始处理，订单号：{}", request.getRechargeOrderNo());

        // 1. 验证充值订单号是否已存在
        if (rechargeOrderDao.existsByRechargeOrderNo(request.getRechargeOrderNo())) {
            throw new BusinessException("充值订单号已存在：" + request.getRechargeOrderNo());
        }

        // 2. 计算总金额和卡数量
        int cardCount = request.getCardInfos().size();
        LocalDateTime now = LocalDateTime.now();
        List<RechargeOrderCardInfo> cardInfos = buildRechargeOrderCardInfos(request, now);
        RechargeOrder rechargeOrder = buildRechargeOrder(request, now);
        RechargeOrderLog operationLog = buildOperationLog(request, now);
        rechargeOrderDao.batchInsert(tx -> {
            // 3. 保存充值订单基本信息
            tx.insert(rechargeOrder).result(false);
            // 4. 批量保存卡信息
            tx.insert(cardInfos).result(false);
            // 5. 记录操作日志
            tx.insert(operationLog).result(false);
        });
        log.info("保存充值订单基本信息成功，订单号：{}", request.getRechargeOrderNo());
        log.info("批量保存卡信息成功，订单号：{}，卡数量：{}", request.getRechargeOrderNo(), cardCount);
        log.info("记录操作日志成功，订单号：{}", request.getRechargeOrderNo());
    }

    /**
     * 构建充值订单实体
     */
    private RechargeOrder buildRechargeOrder(RechargeOrderPushRequest request, LocalDateTime now) {
        RechargeOrder rechargeOrder = new RechargeOrder();
        rechargeOrder.setContractNo(request.getContractNo());
        rechargeOrder.setRechargeOrderNo(request.getRechargeOrderNo());
        rechargeOrder.setCustomerCode(request.getCustomerCode());
        rechargeOrder.setCustomerName(request.getCustomerName());
        rechargeOrder.setSellerId(request.getSellerId());
        rechargeOrder.setSeller(request.getSeller());
        rechargeOrder.setAreaCode(request.getAreaCode());
        rechargeOrder.setAreaName(request.getAreaName());
        rechargeOrder.setCinemaInnerCode(request.getCinemaInnerCode());
        rechargeOrder.setCinemaName(request.getCinemaName());
        rechargeOrder.setCount(rechargeOrder.getCount());
        rechargeOrder.setAmount(rechargeOrder.getAmount());
        rechargeOrder.setStatus(RechargeOrderStatus.WAIT_RECHARGE);
        rechargeOrder.setCreateTime(now);
        rechargeOrder.setUpdateTime(now);
        return rechargeOrder;
    }

    /**
     * 构建充值订单卡信息列表
     */
    private List<RechargeOrderCardInfo> buildRechargeOrderCardInfos(RechargeOrderPushRequest request, LocalDateTime now) {
        List<RechargeOrderCardInfo> cardInfos = new ArrayList<>();
        for (RechargeOrderCardPushInfo cardPushInfo : request.getCardInfos()) {
            RechargeOrderCardInfo cardInfo = new RechargeOrderCardInfo();
            cardInfo.setRechargeOrderNo(request.getRechargeOrderNo());
            cardInfo.setCardNo(cardPushInfo.getCardNo());
            cardInfo.setCardTypeCode(cardPushInfo.getCardTypeCode());
            cardInfo.setCardTypeName(cardPushInfo.getCardTypeName());
            cardInfo.setRechargeAmount(request.getEveryCardRechargeAmount());
            cardInfo.setPresentAmount(request.getEveryCardPresentAmount());
            cardInfo.setChangeBalance(request.getEveryCardRechargeAmount() + request.getEveryCardPresentAmount());
            cardInfo.setBeforeBalance(0); // 推送时暂时设为0，充值时会更新
            cardInfo.setAfterBalance(0);  // 推送时暂时设为0，充值时会更新
            cardInfo.setStatus(CardRechargeStatus.WAIT_RECHARGE);
            cardInfo.setCreateTime(now);
            cardInfo.setUpdateTime(now);
            cardInfos.add(cardInfo);
        }
        return cardInfos;
    }

    /**
     * 构建操作日志
     */
    private RechargeOrderLog buildOperationLog(RechargeOrderPushRequest request, LocalDateTime now) {
        RechargeOrderLog log = new RechargeOrderLog();
        log.setRechargeOrderNo(request.getRechargeOrderNo());
        log.setCreateTime(now);
        log.setOperationType(OperationLogType.CREATE_ORDER);
        log.setOperationLog(String.format("创建充值订单，卡数量：%d，总金额：%d元", request.getCount(), request.getAmount()));
        return log;
    }
}
