package wanda.card.kam.admin.service.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import wanda.card.kam.admin.contract.RechargeOrderPushService;
import wanda.card.kam.admin.contract.dto.RechargeOrderPushDto.RechargeOrderPushRequest;
import wanda.card.kam.admin.service.biz.RechargeOrderPushServiceBiz;
import wanda.card.kam.common.provider.util.RedisLockSupport;
import wanda.stark.core.data.R;

/**
 * 充值订单推送RPC服务控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@AllArgsConstructor
public class RechargeOrderPushController implements RechargeOrderPushService {

    private final RedisLockSupport redisLockSupport;
    private final RechargeOrderPushServiceBiz rechargeOrderPushServiceBiz;

    @Override
    public R pushRechargeOrder(RechargeOrderPushRequest request) {
        log.info("RPC充值订单推送入参:{}", request);
        try {
            redisLockSupport.lockTemplate(request.getRechargeOrderNo(), () -> rechargeOrderPushServiceBiz.pushRechargeOrder(request));
            return R.success();
        } catch (Exception e) {
            log.error("RPC充值订单推送异常", e);
            return R.fail(e.getMessage());
        }
    }
}
