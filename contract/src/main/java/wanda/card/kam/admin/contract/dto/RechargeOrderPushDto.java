package wanda.card.kam.admin.contract.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 充值订单推送DTO
 *
 * <AUTHOR>
 */
public class RechargeOrderPushDto {

    /**
     * 充值订单推送请求
     */
    @Getter
    @Setter
    public static class RechargeOrderPushRequest {
        /**
         * 充值订单号
         */
        @NotBlank(message = "充值订单号不能为空")
        private String rechargeOrderNo;

        /**
         * 合同编码
         */
        @NotBlank(message = "合同编码不能为空")
        private String contractNo;

        /**
         * 客户编码
         */
        @NotBlank(message = "客户编码不能为空")
        private String customerCode;

        /**
         * 客户名称
         */
        @NotBlank(message = "客户名称不能为空")
        private String customerName;

        /**
         * 销售员万信号
         */
        @NotBlank(message = "销售员万信号不能为空")
        private String sellerId;

        /**
         * 销售员姓名
         */
        @NotBlank(message = "销售员姓名不能为空")
        private String seller;

        /**
         * 区域编码
         */
        @NotBlank(message = "区域编码不能为空")
        private String areaCode;

        /**
         * 区域名称
         */
        @NotBlank(message = "区域名称不能为空")
        private String areaName;

        /**
         * 影城内码
         */
        @NotBlank(message = "影城内码不能为空")
        private String cinemaInnerCode;

        /**
         * 影城名称
         */
        @NotBlank(message = "影城名称不能为空")
        private String cinemaName;

        /**
         * 每张卡充值金额（分）
         */
        private Integer everyCardRechargeAmount;

        /**
         * 每张卡赠送金额（分）
         */
        private Integer everyCardPresentAmount;
        /**
         * 卡数量
         */
        private Integer count;

        /**
         * 当前订单的总充值金额（含赠送金额），卡数量*单卡（充值金额+赠送金额）分
         */
        private Integer amount;

        @NotBlank(message = "操作人不能为空")
        private String operator;
        /**
         * 卡信息列表
         */
        @NotEmpty(message = "卡信息列表不能为空")
        @Valid
        private List<RechargeOrderCardPushInfo> cardInfos;
    }

    /**
     * 推送的卡信息
     */
    @Getter
    @Setter
    public static class RechargeOrderCardPushInfo {
        /**
         * 卡号
         */
        @NotBlank(message = "卡号不能为空")
        private String cardNo;

        /**
         * 卡类型编码
         */
        @NotBlank(message = "卡类型编码不能为空")
        private String cardTypeCode;

        /**
         * 卡类型名称
         */
        @NotBlank(message = "卡类型名称不能为空")
        private String cardTypeName;
    }
}
