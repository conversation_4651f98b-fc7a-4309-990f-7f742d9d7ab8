package wanda.card.kam.common.contract.constant;

import lombok.AllArgsConstructor;
import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;

@AllArgsConstructor
public enum RechargeOrderStatus implements EnumValueSupport, EnumDisplayNameSupport {

    WAIT_RECHARGE(1, "待充值"),
    PART_RECHARGE(2, "部分充值"),
    SUCCESS(3, "充值成功"),
    FAILED(4, "充值失败");

    private final int value;
    private final String displayName;

    @Override
    public String displayName() {
        return displayName;
    }

    @Override
    public int value() {
        return value;
    }

    public static RechargeOrderStatus valueOf(int value) {
        return Enums.valueOf(RechargeOrderStatus.class, value);
    }

    public static RechargeOrderStatus valueOf(int value, boolean throwNotFound) {
        return Enums.valueOf(RechargeOrderStatus.class, value, throwNotFound);
    }
}
